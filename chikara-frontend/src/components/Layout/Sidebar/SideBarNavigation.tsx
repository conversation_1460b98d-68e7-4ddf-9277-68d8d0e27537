import { hospitalisedNavItems, jailedNavItems, navItems } from "@/helpers/navItems";
import { cn } from "@/lib/utils";
import { Fragment } from "react";
import { useNormalStore } from "../../../app/store/stores";
import SideBarGridItem from "./SideBarGridItem";
import SideBarGridItemThin from "./SideBarGridItemThin";
import type { User } from "@/types/user";
import type { QuestWithProgress } from "@/types/quest";

interface SideBarNavigationProps {
    availableQuests?: QuestWithProgress[] | undefined;
    currentUser?: User | undefined;
}

export default function SideBarNavigation({ availableQuests, currentUser }: SideBarNavigationProps) {
    const hospitalised = (currentUser?.hospitalisedUntil ?? 0) > 0;
    const jailed = (currentUser?.jailedUntil ?? 0) > 0;
    const inFight = currentUser?.battleValidUntil;
    const { preventNavigation } = useNormalStore();
    let navigation: ReturnType<typeof navItems> = [];

    if (!hospitalised && !jailed) {
        navigation = navItems(currentUser?.userType ?? null);
    } else if (hospitalised) {
        navigation = hospitalisedNavItems(currentUser?.userType ?? null);
    } else {
        navigation = jailedNavItems(currentUser?.userType ?? null);
    }

    if (preventNavigation) {
        return (
            <div
                className={cn(
                    jailed || hospitalised ? "h-2/6" : "h-[45%]",
                    "flex flex-1 flex-col px-2 pt-2 2xl:flex-initial min-h-0"
                )}
            >
                <div
                    className={
                        "hidden size-full grid-cols-3 gap-x-2 gap-y-4 px-1 2xl:mt-1 2xl:grid 2xl:grid-cols-2 2xl:p-1 2xl:content-start"
                    }
                ></div>

                <div className={"flex size-full flex-1 flex-col gap-y-2 px-1 2xl:hidden overflow-y-auto"}></div>
            </div>
        );
    }

    return (
        <div
            className={cn(
                jailed || hospitalised ? "h-2/6" : "h-[45%]",
                "flex flex-1 flex-col px-2 pt-2 2xl:flex-initial min-h-0"
            )}
        >
            {/* Desktop layout (2xl and up) */}
            <div
                className={
                    "hidden size-full grid-cols-3 gap-x-2 gap-y-4 px-1 2xl:mt-1 2xl:grid 2xl:grid-cols-2 2xl:p-1 2xl:content-start"
                }
            >
                {navigation.map((item, index) => (
                    <Fragment key={index}>
                        {item.thin ? null : (
                            <SideBarGridItem
                                item={item}
                                inFight={inFight?.toString() ?? null}
                                availableQuests={availableQuests?.length}
                            />
                        )}
                    </Fragment>
                ))}
                <div className={cn("col-span-2 grid w-full grid-cols-1 gap-x-2 gap-y-[0.6rem] auto-rows-max")}>
                    {navigation.map((item, index) => (
                        <Fragment key={index}>
                            {item.thin ? (
                                <SideBarGridItemThin item={item} inFight={inFight?.toString() ?? null} />
                            ) : null}
                        </Fragment>
                    ))}
                </div>
            </div>

            {/* Mobile/tablet layout */}
            <div className={"flex size-full flex-1 flex-col gap-y-2 px-1 2xl:hidden overflow-y-auto"}>
                {navigation.map((item, index) => (
                    <Fragment key={index}>
                        <div className="flex-shrink-0">
                            <SideBarGridItemThin item={item} inFight={inFight?.toString() ?? null} />
                        </div>
                    </Fragment>
                ))}
            </div>
        </div>
    );
}
