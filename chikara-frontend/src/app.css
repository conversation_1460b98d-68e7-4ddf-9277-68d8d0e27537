.backdrop-blur-firefox::before {
    background-color: rgba(149, 149, 149, 0.2);
    content: "";
    -webkit-filter: blur(18px);
    -moz-filter: blur(18px);
    filter: blur(18px);
    position: absolute;
    inset: 3%;
    z-index: -1;
}

.primaryBtn {
    background: radial-gradient(132.75% 155.27% at 31.94% -11.82%, #9186ff 0%, #6d61ff 33.87%, #574aff 91.62%);
}

.vignette-sm {
    box-shadow: 0 0 40px rgba(0, 0, 0, 0.4) inset;
}

.vignette-lg {
    box-shadow: 0 0 200px rgba(0, 0, 0, 0.7) inset;
}

@keyframes attackSlash {
    0% {
        transform: translate3d(70px, -70px, 0) rotateZ(45deg);
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translate3d(0px, 0px, 0) rotateZ(45deg);
        opacity: 0;
    }
}

.headerGradient {
    background: linear-gradient(to top, transparent, rgba(24, 24, 24, 0.4));
    width: 100%;
}

@keyframes shake-enemy {
    0%,
    100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-5px);
    }
    50% {
        transform: translateX(5px);
    }
    75% {
        transform: translateX(-5px);
    }
}

.ranged-attack-animation {
    animation: shake-enemy 0.2s ease-in-out;
    height: 5px;
    width: 5px;
    box-shadow:
        #000 45px 0,
        #000 10px 5px,
        #000 45px 5px,
        #000 80px 5px,
        #000 15px 10px,
        #000 20px 10px,
        #000 40px 10px,
        #ff0 45px 10px,
        #000 50px 10px,
        #000 70px 10px,
        #000 75px 10px,
        #000 15px 15px,
        #ff0 20px 15px,
        #000 25px 15px,
        #000 30px 15px,
        #000 35px 15px,
        #ff0 40px 15px,
        #ff0 45px 15px,
        #ff0 50px 15px,
        #000 55px 15px,
        #000 60px 15px,
        #000 65px 15px,
        #ff0 70px 15px,
        #000 75px 15px,
        #000 20px 20px,
        #ff0 25px 20px,
        #ff0 30px 20px,
        #ff0 35px 20px,
        #ff0 40px 20px,
        #ffb 45px 20px,
        #ff0 50px 20px,
        #ff0 55px 20px,
        #ff0 60px 20px,
        #ff0 65px 20px,
        #000 70px 20px,
        #000 20px 25px,
        #ff0 25px 25px,
        #ff0 30px 25px,
        #ffb 35px 25px,
        #ffb 40px 25px,
        #ffb 45px 25px,
        #ffb 50px 25px,
        #ffb 55px 25px,
        #ff0 60px 25px,
        #ff0 65px 25px,
        #000 70px 25px,
        #000 20px 30px,
        #ff0 25px 30px,
        #ff0 30px 30px,
        #ffb 35px 30px,
        #ffb 40px 30px,
        #fff 45px 30px,
        #ffb 50px 30px,
        #ffb 55px 30px,
        #ff0 60px 30px,
        #ff0 65px 30px,
        #000 70px 30px,
        #000 15px 35px,
        #ff0 20px 35px,
        #ff0 25px 35px,
        #ffb 30px 35px,
        #ffb 35px 35px,
        #fff 40px 35px,
        #fff 45px 35px,
        #fff 50px 35px,
        #ffb 55px 35px,
        #ffb 60px 35px,
        #ff0 65px 35px,
        #ff0 70px 35px,
        #000 75px 35px,
        #000 5px 40px,
        #000 10px 40px,
        #ff0 15px 40px,
        #ff0 20px 40px,
        #ffb 25px 40px,
        #ffb 30px 40px,
        #fff 35px 40px,
        #fff 40px 40px,
        #fff 45px 40px,
        #fff 50px 40px,
        #fff 55px 40px,
        #ffb 60px 40px,
        #ffb 65px 40px,
        #ff0 70px 40px,
        #ff0 75px 40px,
        #000 80px 40px,
        #000 85px 40px,
        #000 15px 45px,
        #ff0 20px 45px,
        #ff0 25px 45px,
        #ffb 30px 45px,
        #ffb 35px 45px,
        #fff 40px 45px,
        #fff 45px 45px,
        #fff 50px 45px,
        #ffb 55px 45px,
        #ffb 60px 45px,
        #ff0 65px 45px,
        #ff0 70px 45px,
        #000 75px 45px,
        #000 20px 50px,
        #ff0 25px 50px,
        #ff0 30px 50px,
        #ffb 35px 50px,
        #ffb 40px 50px,
        #fff 45px 50px,
        #ffb 50px 50px,
        #ffb 55px 50px,
        #ff0 60px 50px,
        #ff0 65px 50px,
        #000 70px 50px,
        #000 20px 55px,
        #ff0 25px 55px,
        #ff0 30px 55px,
        #ffb 35px 55px,
        #ffb 40px 55px,
        #ffb 45px 55px,
        #ffb 50px 55px,
        #ffb 55px 55px,
        #ff0 60px 55px,
        #ff0 65px 55px,
        #000 70px 55px,
        #000 20px 60px,
        #ff0 25px 60px,
        #ff0 30px 60px,
        #ff0 35px 60px,
        #ff0 40px 60px,
        #ffb 45px 60px,
        #ff0 50px 60px,
        #ff0 55px 60px,
        #ff0 60px 60px,
        #ff0 65px 60px,
        #000 70px 60px,
        #000 15px 65px,
        #ff0 20px 65px,
        #000 25px 65px,
        #000 30px 65px,
        #000 35px 65px,
        #ff0 40px 65px,
        #ff0 45px 65px,
        #ff0 50px 65px,
        #000 55px 65px,
        #000 60px 65px,
        #000 65px 65px,
        #ff0 70px 65px,
        #000 75px 65px,
        #000 15px 70px,
        #000 20px 70px,
        #000 40px 70px,
        #ff0 45px 70px,
        #000 50px 70px,
        #000 70px 70px,
        #000 75px 70px,
        #000 10px 75px,
        #000 45px 75px,
        #000 80px 75px;
}

.slash {
    margin: 0 auto;
    height: 5px;
    width: 5px;
    z-index: 3;
    border-left: 2px solid transparent;
    border-right: 2px solid transparent;
    border-bottom: 120px solid red;
    transform: rotateZ(45deg);
    animation: attackSlash 0.205s ease-in 1;
    animation-iteration-count: 1;
}

@keyframes shake {
    0% {
        transform: translate(1px, 1px) rotate(0deg);
    }
    10% {
        transform: translate(-1px, -2px) rotate(-1deg);
    }
    20% {
        transform: translate(-3px, 0px) rotate(1deg);
    }
    30% {
        transform: translate(3px, 2px) rotate(0deg);
    }
    40% {
        transform: translate(1px, -1px) rotate(1deg);
    }
    50% {
        transform: translate(-1px, 2px) rotate(-1deg);
    }
    60% {
        transform: translate(-3px, 1px) rotate(0deg);
    }
    70% {
        transform: translate(3px, 1px) rotate(-1deg);
    }
    80% {
        transform: translate(-1px, -1px) rotate(1deg);
    }
    90% {
        transform: translate(1px, 2px) rotate(0deg);
    }
    100% {
        transform: translate(1px, -2px) rotate(-1deg);
    }
}

.damageShake {
    animation: shake 0.5s;
    animation-iteration-count: 1;
}

@keyframes damageFly {
    0% {
        transform: translateX(0px) translateY(0px) translateZ(0px);
    }

    75% {
        transform: translateX(0px) translateY(-30px) translateZ(0px);
        opacity: 1;
    }

    100% {
        transform: translateX(0px) translateY(-50px) translateZ(0px);
        opacity: 0;
    }
}

.damageText {
    color: #ffdc11;
    -webkit-text-stroke-width: 1px;
    -webkit-text-stroke-color: black;
    font-size: 36px;
    animation: damageFly 0.6s ease-in 0.01s 1 forwards;
}

.healText {
    color: #46ff1c;
    -webkit-text-stroke-width: 1px;
    -webkit-text-stroke-color: black;
    font-size: 36px;
    animation: damageFly 0.6s ease-in 0.1s 1 forwards;
}

.bleedText {
    color: #ec1b10;
    -webkit-text-stroke-width: 1px;
    -webkit-text-stroke-color: black;
    font-size: 36px;
    animation: damageFly 0.6s ease-in 0.1s 1 forwards;
}

@keyframes fleeFailFly {
    0% {
        transform: translateX(0px) translateY(0px) translateZ(0px);
    }

    75% {
        opacity: 1;
    }

    100% {
        transform: translateX(0px) translateY(-90px) translateZ(0px);
        opacity: 0;
    }
}

@keyframes fleeSuccessFly {
    0% {
        transform: translateX(0px) translateY(0px) translateZ(0px);
    }

    75% {
        opacity: 1;
    }

    100% {
        transform: translateX(0px) translateY(-90px) translateZ(0px);
        opacity: 0;
    }
}

.fleeFailText {
    color: #f1180c;
    -webkit-text-stroke-width: 1px;
    -webkit-text-stroke-color: rgb(167, 46, 46);
    animation: fleeFailFly 0.6s ease-in 0.01s 1 forwards;
}

.fleeFailTextTemp {
    position: absolute;
    opacity: 1;
    top: 250px;
    left: 60px;
    right: 0px;
    margin: 0 auto;
    height: 100px;
    width: 300px;
    z-index: 5;
    color: #f1180c;
    -webkit-text-stroke-width: 1px;
    -webkit-text-stroke-color: rgb(167, 46, 46);
    font-size: 44px;
    animation: damageFly 0.8s ease-in 0.01s 1;
}

.fleeSuccessText {
    color: #ffdc11;
    -webkit-text-stroke-width: 1px;
    -webkit-text-stroke-color: black;
    animation: fleeSuccessFly 0.6s ease-in 0.01s 1 forwards;
}

/* // SHOPS // */

.shopKeeperAvatar {
    transform: scale(1.6);
    transition:
        transform 0.5s,
        filter 0.5s ease-in-out;
    filter: brightness(95%);
    filter: saturate(100%);
}

.shopKeeperAvatarWrapper {
    border: 3px solid rgb(30, 30, 30);
    background-image: url("");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 800px 500px;
}

.shopKeeperAvatarWrapper:hover .shopKeeperAvatar {
    filter: brightness(103%);
    transform: scale(1.7) translateY(2%);
    cursor: pointer;
    filter: saturate(105%);
}

.modalHeaderBackground {
    background-image: url("./assets/images/UI/BackgroundImages/diagmonds-light.png");
    background-repeat: repeat;
    background-position: left;
}

.gradientBackground {
    background-image: url("./assets/images/UI/BackgroundImages/cartoonPattern.png");
    background-repeat: repeat;
    background-position: bottom;
    mask-image: -webkit-gradient(linear, left 60%, left top, from(rgba(0, 0, 0, 1)), to(rgba(0, 0, 0, 0)));
}

.gradientBackgroundFull {
    background-image: url("./assets/images/UI/BackgroundImages/cartoonPatternFull.png");
    background-repeat: repeat;
}

.shopKeeperName {
    font-family: "Lilita One";
    font-weight: 500;
    text-transform: uppercase;
    position: absolute;
    bottom: 0%;
    z-index: 99;
    color: white;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.75));
    width: 100%;
    cursor: pointer;
}

.disabledShopKeeperName {
    font-family: "Lilita One";
    font-weight: 500;
    text-transform: uppercase;
    position: absolute;
    bottom: 0%;
    z-index: 99;
    color: white;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.75));
    width: 100%;
}

.disabledShop {
    transform: scale(1.7) translateY(2%);
    transition:
        transform 0.5s,
        filter 0.5s ease-in-out;
    filter: brightness(0%);
}

.shopKeeperAvatarWrapper:hover .shopKeeperName {
    background: linear-gradient(transparent, rgba(112, 96, 254, 0.9));
    text-shadow: 1px 0 10px rgb(255 255 255 / 50%);
    transition: all 0.2s ease-in-out;
    transform: scale(1.1);
}

.shopKeeperAvatarWrapper:hover .traderRepContainer {
    text-shadow: 1px 0 10px rgb(255 255 255 / 50%);
    transition: all 0.2s ease-in-out;
    transform: scale(1.03) translateX(-49%);
}

.shopKeeperAvatarWrapper:hover .disabledShopKeeperName {
    background: linear-gradient(transparent, rgba(104, 104, 104, 0.9));
    /* text-shadow: 1px 0 10px rgb(255 255 255 / 50%);
  transition: all 0.2s ease-in-out;
  transform: scale(1.1); */
}

.shoppingIcon {
    opacity: 0;
    transition:
        display 0s,
        opacity 0.5s linear;
}

.shopKeeperAvatarWrapper:hover .shopKeeperName .shoppingIcon {
    opacity: 100%;
}

.centerGridHeader .ag-header-cell-label {
    justify-content: center;
}

/* // CHAT // */

.globalChatBoxShadow {
    box-shadow: -10px 0px 40px 0px rgb(94 92 154 / 6%);
}

.chatMessageBox {
    border: 2px solid rgb(75 85 99 / 0.5);
}

.chatMessageBox:focus-within {
    border: 2px solid rgb(99 102 241 / 1);
}

.chatMessageBoxError {
    background: #dee2e9;
    border: 2px solid transparent;
}

.chatMessageBoxError:focus-within {
    border: 2px solid rgb(195, 41, 17);
}

.chatTextArea {
    border: none;
    resize: none;
    outline: none;
    overflow: auto;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    width: 80%;
    overflow: hidden;
}

.chatTextArea:focus {
    border: none;
    overflow: auto;
    outline: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    resize: none;
}

.chatMessageAvatar {
    top: 6px;
    left: 6px;
}

.chatMessageWrapper:hover {
    border: 2px solid rgba(56, 130, 221, 0.3);
}

.chatMessageWrapperAdmin {
    border: 2px solid transparent;
    background:
        padding-box linear-gradient(#1f1f2d, #1f1f2d),
        border-box linear-gradient(rgba(199, 2, 2, 0.5), rgb(75 85 99 / 0.25));
}

.chatMessageWrapperAdmin:hover {
    border: 2px solid transparent;
    background:
        padding-box linear-gradient(#1f1f2d, #1f1f2d),
        border-box linear-gradient(rgba(199, 2, 2, 0.7), rgb(255 255 255 / 0.1));
}

.chatMessageWrapperMention {
    border: 2px solid transparent;
    background:
        padding-box linear-gradient(#1f1f2d, #1f1f2d),
        border-box linear-gradient(rgba(243, 231, 3, 0.8), rgb(255 255 255 / 0.1));
}

.chatMessageWrapperMention:hover {
    border: 2px solid transparent;
    background:
        padding-box linear-gradient(#1f1f2d, #1f1f2d),
        border-box linear-gradient(rgba(243, 231, 3, 0.9), rgb(255 255 255 / 0.1));
}

.chatMessageConsole {
    border: 2px solid transparent;
    background:
        padding-box linear-gradient(#1f1f2d, #1f1f2d),
        border-box linear-gradient(rgba(221, 204, 11, 0.561), rgb(75 85 99 / 0.25));
}

.bounce {
    -moz-animation: bounce 3s infinite;
    -webkit-animation: bounce 3s infinite;
    animation: bounce 3s infinite;
}

@-moz-keyframes bounce {
    0%,
    20%,
    50%,
    80%,
    100% {
        -moz-transform: translateY(0);
        transform: translateY(0);
    }
    40% {
        -moz-transform: translateY(-30px);
        transform: translateY(-30px);
    }
    60% {
        -moz-transform: translateY(-15px);
        transform: translateY(-15px);
    }
}
@-webkit-keyframes bounce {
    0%,
    20%,
    50%,
    80%,
    100% {
        -webkit-transform: translateY(0);
        transform: translateY(0);
    }
    40% {
        -webkit-transform: translateY(-30px);
        transform: translateY(-30px);
    }
    60% {
        -webkit-transform: translateY(-15px);
        transform: translateY(-15px);
    }
}
@keyframes bounce {
    0%,
    20%,
    50%,
    80%,
    100% {
        -moz-transform: translateY(0);
        -ms-transform: translateY(0);
        -webkit-transform: translateY(0);
        transform: translateY(0);
    }
    40% {
        -moz-transform: translateY(-30px);
        -ms-transform: translateY(-30px);
        -webkit-transform: translateY(-30px);
        transform: translateY(-30px);
    }
    60% {
        -moz-transform: translateY(-15px);
        -ms-transform: translateY(-15px);
        -webkit-transform: translateY(-15px);
        transform: translateY(-15px);
    }
}

.cross {
    width: 50px;
    height: 50px;
}

.cross:before,
.cross:after {
    position: absolute;
    left: 24px;
    content: " ";
    height: 48px;
    width: 4px;
    background-color: #af0b0b;
}
.cross:before {
    transform: rotate(45deg);
}
.cross:after {
    transform: rotate(-45deg);
}

.boxtest {
    color: rgb(245, 245, 246);
    transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
    border-radius: 6px;
    border: 0px solid;
    padding: 1px;
    background-position: initial;
    background-size: initial;
    background-repeat: initial;
    background-attachment: initial;
    background-origin: initial;
    background-clip: initial;
    background-color: rgb(67, 71, 75);
    overflow: visible;
    width: 100%;
    background-image: none !important;
}

.innerBoxTest {
    border-radius: inherit;
    background: rgb(28, 33, 37);
}

.testTab {
    display: inline-flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
    outline: 0px;
    margin: 0px 10px 0px 0px;
    cursor: pointer;
    user-select: none;
    vertical-align: middle;
    appearance: none;
    text-decoration: none;
    font-family:
        Inter,
        -apple-system,
        BlinkMacSystemFont,
        "Segoe UI",
        Helvetica,
        Arial,
        sans-serif,
        "Apple Color Emoji",
        "Segoe UI Emoji";
    line-height: 1.25;
    max-width: 360px;
    position: relative;
    min-height: 48px;
    flex-shrink: 0;
    white-space: normal;
    text-align: center;
    flex-direction: column;
    text-transform: none;
    min-width: 0px;
    font-size: 15px;
    letter-spacing: -0.025px;
    font-weight: 600;
    padding: 10px 22px;
    color: rgb(179, 181, 183);
    background: rgba(250, 250, 250, 0.01);
    border-radius: 6px 6px 0px 0px;
    border-width: 1px 1px 0px;
    border-top-style: solid;
    border-right-style: solid;
    border-left-style: solid;
    border-top-color: rgba(67, 71, 75, 0.3);
    border-right-color: rgba(67, 71, 75, 0.3);
    border-left-color: rgba(67, 71, 75, 0.3);
    border-image: initial;
    border-bottom-style: initial;
    border-bottom-color: initial;
    overflow: visible;
}

.testSelectedTab {
    border-color: rgba(137, 140, 142, 0.4) !important;
    display: inline-flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
    outline: 0px;
    margin: 0px 10px 0px 0px;
    cursor: pointer;
    user-select: none;
    vertical-align: middle;
    appearance: none;
    text-decoration: none;
    font-family:
        Inter,
        -apple-system,
        BlinkMacSystemFont,
        "Segoe UI",
        Helvetica,
        Arial,
        sans-serif,
        "Apple Color Emoji",
        "Segoe UI Emoji";
    line-height: 1.25;
    max-width: 360px;
    position: relative;
    min-height: 48px;
    flex-shrink: 0;
    white-space: normal;
    text-align: center;
    flex-direction: column;
    text-transform: none;
    min-width: 0px;
    font-size: 15px;
    letter-spacing: -0.025px;
    font-weight: 600;
    padding: 10px 22px;
    color: rgb(179, 181, 183);
    background: rgba(250, 250, 250, 0.01);
    border-radius: 6px 6px 0px 0px;
    border-width: 1px 1px 0px;
    border-top-style: solid;
    border-right-style: solid;
    border-left-style: solid;
    border-top-color: rgba(67, 71, 75, 0.3);
    border-right-color: rgba(67, 71, 75, 0.3);
    border-left-color: rgba(67, 71, 75, 0.3);
    border-image: initial;
    border-bottom-style: initial;
    border-bottom-color: initial;
    overflow: visible;
}

.bg-hero-polka-dots {
    background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%239C92AC' fill-opacity='0.4' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='3'/%3E%3Ccircle cx='13' cy='13' r='3'/%3E%3C/g%3E%3C/svg%3E");
}
.roulette-table-container {
    aspect-ratio: auto !important;
    font-size: 1rem !important;
}
.roulette-table-container-first {
    display: none !important;
}
.roulette-table-container-second {
    height: 34% !important;
    width: 100% !important;
}
.roulette-table-container-third {
    height: 66% !important;
    width: 100% !important;
}

@keyframes simpleEntrance {
    0% {
        opacity: 0%;
        transform: translate3d(-2000px, 500px, 0) scale(10);
    }

    1% {
        opacity: 100%;
    }

    40% {
        transform: translate3d(2000px, 0px, 0) scale(5);
    }
    50% {
        transform: translate3d(-2000px, -100px, 0) scale(3);
    }
    60% {
        transform: translate3d(2000px, 100px, 0) scale(2);
    }
    70% {
        transform: translate3d(-2000px, 400px, 0) scale(0.1);
    }

    75% {
        transform: translate3d(0px, 0px, 0) scale(0.1);
    }

    80% {
        transform: rotate(360deg) translateX(150px) rotate(-360deg);
    }
}

.simpleEntrance {
    animation-timing-function: linear;
    transform-origin: bottom center;
    animation-name: simpleEntrance;
    animation-duration: 10s;
}

.cardBorder1 {
    box-shadow:
        rgb(13, 16, 19) 0px 0px 0px 1px,
        rgba(99, 102, 241, 0.3) 0px 9px 16px,
        rgba(13, 16, 19, 0.34) 0px 2px 2px;
    transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.barChartBG {
    color: rgb(245, 245, 246);
    transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
    border-radius: 6px;
    box-shadow:
        rgb(13, 16, 19) 0px 0px 0px 1px,
        rgba(13, 16, 19, 0.3) 0px 9px 16px,
        rgba(13, 16, 19, 0.34) 0px 2px 2px;
    background-color: rgb(28, 33, 37);
    overflow: hidden;
    height: 100%;
    flex-direction: column;
    display: flex;
    background-image: none !important;
}

.statBoxBig {
    color: rgb(245, 245, 246);
    transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
    border-radius: 6px;
    box-shadow:
        rgb(13, 16, 19) 0px 0px 0px 1px,
        rgba(13, 16, 19, 0.3) 0px 9px 16px,
        rgba(13, 16, 19, 0.34) 0px 2px 2px;
    background-color: rgb(28, 33, 37);
    position: relative;
    overflow: visible;
    text-align: center;
    background-image: none !important;
}

.statBoxBig::before {
    position: absolute;
    content: "";
    height: 6px;
    left: -1px;
    width: calc(100% + 2px);

    bottom: -2px;
    border-bottom-left-radius: inherit;
    border-bottom-right-radius: inherit;
}

@keyframes slowZoomCenter {
    0% {
        transform: translate3d(0px, 0px, 0) scale(1);
        background-color: rgba(0, 0, 0, 0.7);
    }

    50% {
        transform: translate3d(calc(-18dvw - 50%), calc(30vh - 50%), 0) scale(4);
        background-color: rgb(0, 0, 0, 1);
    }

    100% {
        transform: translate3d(calc(-18dvw - 50%), calc(30vh - 50%), 0) scale(4);
        background-color: rgb(0, 0, 0, 1);
    }
}

.slowZoomCenter {
    animation-timing-function: linear;
    animation-name: slowZoomCenter;
    animation-duration: 20s;
    animation-fill-mode: forwards; /* Keeps the final state after animation ends */
}

.topDivider {
    top: calc((100% - 2rem) / 5);
}

.secondDivider {
    top: calc(2 * (100% - 0.4rem) / 5);
}

.thirdDivider {
    top: calc(3 * (100% - 0rem) / 5);
}

.fourthDivider {
    top: calc(4 * (100% + 0.3rem) / 5);
}

.talentInfoModalAnim {
    filter: drop-shadow(rgba(12, 49, 170, 0.5) 0px 0px 24px);
    transition:
        transform 0.35s ease-in-out 0s,
        -webkit-transform 0.35s ease-in-out 0s;
}

.primary {
    background-color: #615dfa;
}

.header {
    /* background-color: #ffffff; */
    filter: drop-shadow(0px 1px 4px rgba(0, 0, 0, 0.07));
}

.sidebar {
    background-color: #ffffff;
    filter: drop-shadow(0px 0px 15px rgba(0, 0, 0, 0.05));
    border-right: 1px solid #ccc;
}

.sidebar_text {
    color: #656565;
}

.sidebar_textbg_active {
    background-color: rgba(235, 248, 255, 0.9);
}

.sidebar_text_username {
    color: #656565;
}

.header_text {
    color: #ffffff;
}

.sidebar_button {
    background-color: rgba(20, 171, 249, 1);
    color: rgb(255, 255, 255);
}

.notification_bubble {
    width: 23px;
    height: 23px;
    border-radius: 50%;
    border: none;
    background-color: #fe0000;
}

.text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.text-shadow-md {
    text-shadow:
        0 4px 8px rgba(0, 0, 0, 0.12),
        0 2px 4px rgba(0, 0, 0, 0.08);
}

.text-shadow-lg {
    text-shadow:
        0 15px 30px rgba(0, 0, 0, 0.11),
        0 5px 15px rgba(0, 0, 0, 0.08);
}

.text-shadow-none {
    text-shadow: none;
}

.sidebar_shadow {
    box-shadow: 0 0 40px 0 rgba(94, 92, 154, 0.1);
}

.gridIcon svg {
    display: none;
}

.gridIconHover svg {
    display: none;
}

.gridIconHover:hover svg {
    display: block;
}

.dottedOverlay {
    background-image: url("/src/assets/icons/UI/dottedOverlay.gif");
    background-repeat: repeat;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 0.375rem;
}

.blackImageOverlay {
    content: "";
    display: block;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    opacity: 0.85;
    background-image: linear-gradient(180deg, transparent 0, #000);
}

.blackImageOverlayContainer:hover .blackImageOverlay {
    content: "";
    display: block;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    opacity: 0.85;
    background-image: linear-gradient(180deg, transparent 0, rgb(81, 12, 121));
}

.background {
    width: 300px;
    height: 100px;
    background: lightgrey;

    -webkit-transform: skewX(-6deg);
    transform: skewX(-6deg);
    border: 2px solid black;
}
.foreground {
    width: 280px;
    height: 100px;
    position: absolute;
    top: 60px;
    padding: 10px;
    text-align: center;
}
.dialog {
    width: 300px;
    height: 100%;
    position: relative;
}

.fading-text {
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
}

.fading-text-visible {
    opacity: 1;
}

.bannerHeadingBG {
    background-color: rgba(0, 0, 0, 0.65);
}

.arrow-down {
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid rgba(0, 0, 0, 0.7);
}

.react-flow__pane {
    cursor: auto !important;
}

.react-flow__edge {
    cursor: auto !important;
}

.roundedBtn {
    transition: all 0.3s ease 0s;
    box-shadow: 0 10px #f9ca24;
}

.roundedBtn:active {
    box-shadow: 0 5px #f0932b;
    transform: translateY(5px);
}

.roundedBtnBlue {
    background: #143d74;
    transition: all 0.3s ease 0s;
    box-shadow: 0 10px #0b264e;
}

.roundedBtnBlue:active {
    box-shadow: 0 5px #071a35;
    transform: translateY(5px);
}

.roundedBtnLightBlue {
    background: #1d59a7;
    transition: all 0.3s ease 0s;
    box-shadow: 0 10px #0e3064;
}

.roundedBtnLightBlue:active {
    box-shadow: 0 5px #0c2b57;
    transform: translateY(5px);
}

.roundedBtnBlueThin {
    background: #143d74;
    transition: all 0.3s ease 0s;
    box-shadow: 0 4px #0b264e;
}

.roundedBtnBlueThin:active {
    box-shadow: 0 2px #071a35;
    transform: translateY(5px);
}

.roundedBtnLightBlueThin {
    background: #1d59a7;
    transition: all 0.3s ease 0s;
    box-shadow: 0 4px #0e3064;
}

.roundedBtnLightBlueThin:active {
    box-shadow: 0 2px #0c2b57;
    transform: translateY(5px);
}

.skewContainer {
    transform: skewX(15deg);
}

.skewContainer:nth-child(2) {
    width: 20px;
    margin-left: 30px;
    position: relative;
    top: 12%;
}

.sliderBG {
    border-width: 1rem;
    border-image-source: url("./assets/images/UI/ProgressBars/sliderBG.png");
    border-image-slice: 30% 49% fill;
    border-image-repeat: stretch;
}

.expSliderBG {
    border-width: 1rem;
    border-image-source: url("./assets/images/UI/ProgressBars/expSliderBG.png");
    border-image-slice: 49% 49% fill;
    border-image-repeat: stretch;
}
.expSliderFill {
    border-image-width: 0.85rem 0.5rem;
    border-image-source: url("./assets/images/UI/ProgressBars/expSliderFill.png");
    border-image-slice: 49% fill;
    border-image-repeat: stretch;
}
.yellowSliderFill {
    border-image-width: 1rem;
    border-image-source: url("./assets/images/UI/ProgressBars/yellowSliderFill.png");
    border-image-slice: 49% fill;
    border-image-repeat: stretch;
}

.greenSliderFill {
    border-image-width: 1rem;
    border-image-source: url("./assets/images/UI/ProgressBars/greenSliderFill.png");
    border-image-slice: 49% fill;
    border-image-repeat: stretch;
}

.blueSliderFill {
    border-image-width: 1rem;
    border-image-source: url("./assets/images/UI/ProgressBars/blueSliderFill.png");
    border-image-slice: 49% fill;
    border-image-repeat: stretch;
}

.skyblueButtonBG {
    border-image-source: url("./assets/images/UI/Buttons/skyblueButtonBG.png");
    border-image-slice: 0 50% 0 49% fill;
    border-image-width: 0px 20px 0px 20px;
    border-image-repeat: stretch stretch;
}

.skyblueButtonBGSVG {
    border-image-source: url("./assets/images/UI/Buttons/skyblueButtonBG.svg");
    border-image-slice: 0 50% 0 49% fill;
    border-image-width: 0px 20px 0px 20px;
    border-image-repeat: stretch stretch;
}

.darkBlueButtonBG {
    border-image-source: url("./assets/images/UI/Buttons/darkBlueButtonBG.png");
    border-image-slice: 0 50% 0 49% fill;
    border-image-width: 0px 20px 0px 20px;
    border-image-repeat: stretch stretch;
}

.darkBlueButtonBGSVG {
    border-image-source: url("./assets/images/UI/Buttons/darkBlueButtonBG.svg");
    border-image-slice: 2 50% 0 49% fill;
    border-image-width: 0px 20px 0px 20px;
    border-image-repeat: stretch stretch;
}

.grayButtonBG {
    border-image-source: url("./assets/images/UI/Buttons/grayButtonBG.png");
    border-image-slice: 0 50% 0 49% fill;
    border-image-width: 0px 20px 0px 20px;
    border-image-repeat: stretch stretch;
}

.grayButtonBGSVG {
    border-image-source: url("./assets/images/UI/Buttons/grayButtonBG.svg");
    border-image-slice: 0 50% 0 49% fill;
    border-image-width: 0px 20px 0px 20px;
    border-image-repeat: stretch stretch;
}

.squareBtnBlueBG {
    border-width: 1rem;
    border-image-source: url("./assets/images/UI/Buttons/squareBtnBlueBG.png");
    border-image-slice: 30% 49% fill;
    border-image-repeat: stretch;
    transition: all 0.2s ease 0s;
}

.squareBtnBlueBG:active {
    transform: translateY(5px);
}

.skewedContainer {
    position: relative;
}

.skewedContainer:before {
    content: "";
    position: absolute;
    top: -2px;
    right: -25px;
    bottom: -2px;
    width: 50px;
    -webkit-transform: skew(-20deg);
    -moz-transform: skew(-20deg);
    -ms-transform: skew(-20deg);
    transform: skew(-20deg);
}

.moneyFrame:before {
    --dotSize: 0.15rem;
    --bgSize: 1.3rem;
    --bgPosition: calc(var(--bgSize) / 2);
    content: "";
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: 0.5;
    mask-image: linear-gradient(rgb(0 0 0), rgba(0, 0, 0, 0.475));
    background-image:
        radial-gradient(circle at center, rgba(0, 0, 0, 0.694) var(--dotSize), transparent 0),
        radial-gradient(circle at center, rgba(255, 255, 255, 0.283) var(--dotSize), transparent 0);
    background-size: 1.3rem 1.3rem;
    background-position:
        0 0,
        var(--bgPosition) var(--bgPosition);
}

.rotate {
    animation: rotate 4s linear infinite;
}

@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.wavyBackground {
    background-color: #e5e5f7;
    opacity: 0.1;
    background-size: 4px 4px;
    background-image: repeating-linear-gradient(45deg, #000000 0, #000000 0.4px, #e5e5f7 0, #e5e5f7 50%);
}

.wavyBackgroundAfter {
    z-index: 50;
    content: "";
    position: absolute;
    top: -2px;
    bottom: -2px;
    -webkit-transform: skew(-20deg);
    -moz-transform: skew(-20deg);
    -ms-transform: skew(-20deg);
    transform: skew(-20deg);
}

.overlayDim {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.75);
    z-index: 25;
}

.updateImageClipping {
    clip-path: inset(-19px 0px 0px 0px);
}

/* For mobile phones: */
@media only screen and (max-width: 768px) {
    .updateImageClipping {
        clip-path: inset(-44px 0px 0px 0px);
    }
}

.innerShadow {
    box-shadow: inset 0 0 10px #1e3b8a8c;
}

.cityImagePosition {
    object-position: 30% 1%;
}

.ribbonPurple {
    border-style: solid;
    border-width: 0px 104px 0px 108px;
    -moz-border-image: url("./assets/images/UI/ribbonPurple.png") 0 150;
    -webkit-border-image: url("./assets/images/UI/ribbonPurple.png") 0 150;
    -o-border-image: url("./assets/images/UI/ribbonPurple.png") 0 150;
    border-image: url("./assets/images/UI/ribbonPurple.png") 0 150 fill;
}

.ribbonRed {
    border-style: solid;
    border-width: 0px 104px 0px 108px;
    -moz-border-image: url("./assets/images/UI/ribbonRed.png") 0 150;
    -webkit-border-image: url("./assets/images/UI/ribbonRed.png") 0 150;
    -o-border-image: url("./assets/images/UI/ribbonRed.png") 0 150;
    border-image: url("./assets/images/UI/ribbonRed.png") 0 150 fill;
}

.animate-pop {
    animation-duration: 0.5s;
    animation-name: animate-pop;
    animation-timing-function: cubic-bezier(0.26, 0.53, 0.74, 1);
}

@keyframes animate-pop {
    0% {
        opacity: 0;
        transform: scale(0.5, 0.5);
    }

    100% {
        opacity: 1;
        transform: scale(1, 1);
    }
}

.anim-delay {
    animation-timing-function: ease-in-out;
    animation-fill-mode: backwards;
    animation-delay: 0.5s;
}

.glitch-screen {
    animation: glitch-animation 4s linear infinite;
}

@keyframes glitch-animation {
    0%,
    16.67% {
        /* 1s out of 6s */
        clip-path: inset(0);
        transform: skew(0deg);
        filter: hue-rotate(0deg);
    }
    1.67%,
    3.33%,
    5%,
    6.67% {
        /* Quick changes during the first second */
        clip-path: inset(0 0 0 0);
        transform: skew(5deg);
        filter: hue-rotate(90deg);
    }
    2.5%,
    4.17%,
    5.83% {
        /* Quick changes during the first second */
        clip-path: inset(0 0 0 0);
        transform: skew(-5deg);
        filter: hue-rotate(-90deg);
    }
    17%,
    100% {
        /* Remainder of the time (approx 5 seconds) */
        clip-path: inset(0);
        transform: skew(0deg);
        filter: hue-rotate(0deg);
    }
}

.tv-static {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-size: 200% 200%; /* Larger background size for more variance */
    background-image:
        repeating-linear-gradient(-180deg, white 0%, white 25%, black 25%, black 50%),
        repeating-linear-gradient(180deg, black 0%, black 25%, white 25%, white 50%);
    animation: static-animation 0.05s steps(2, end) infinite;
}

@keyframes static-animation {
    0%,
    100% {
        background-position:
            0% 0%,
            100% 100%;
    }
    25% {
        background-position:
            50% 50%,
            150% 150%;
    }
    50% {
        background-position:
            100% 100%,
            200% 200%;
    }
    75% {
        background-position:
            150% 150%,
            50% 50%;
    }
}
